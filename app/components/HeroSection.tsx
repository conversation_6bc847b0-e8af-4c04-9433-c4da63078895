import { Button } from "@/components/ui/button";
import bookCover from "@/assets/dominion-playbook-cover.jpg";
import { useNavigate } from "react-router-dom";

interface HeroSectionProps {
  onGetStarted: () => void;
}

export const HeroSection = ({ onGetStarted }: HeroSectionProps) => {
  const navigate = useNavigate();

  return (
    <section className="flex items-center h-screen justify-center px-8 overflow-hidden">
      {/* Background pattern */}
      <div className=" inset-0 network-pattern opacity-30" />
      
      {/* Floating elements */}
      {/* <div className="absolute top-20 left-10 w-2 h-2 bg-primary rounded-full floating-animation" />
      <div className="absolute top-40 right-20 w-1 h-1 bg-primary/60 rounded-full floating-animation" style={{animationDelay: '2s'}} />
      <div className="absolute bottom-32 left-1/4 w-1.5 h-1.5 bg-primary/80 rounded-full floating-animation" style={{animationDelay: '4s'}} /> */}
      
      <div className="container mx-auto p-2">
        <div className="grid lg:grid-cols-2 gap-10 items-center">
          {/* Left content */}
          <div className="space-y-8 fade-in-up">
            <div className="space-y-4">
              <h1 className="text-5xl lg:text-4xl font-bold leading-tight">
                The <span className="gradient-text">Dominion</span> Network Playbook
              </h1>
              {/* <p className="text-xl text-muted-foreground leading-relaxed">
                Forget what they told you about networking. Handshakes, business cards, forced small talk—it's all noise.
              </p> */}
            </div>

            <div className="space-y-6">
              {/* <p className="text-lg text-foreground/90">
              
              </p>
               */}
              <p className="text-lg text-foreground/90">
                You'll learn how to attract allies, become unforgettable in a room, and build a network that moves with you—without selling your soul or acting like someone you're not.
              </p>

              <div className="bg-card/50 border border-primary/20 rounded-lg p-6">
                <p className="text-primary font-semibold text-lg">
                  No fluff. No recycled advice. Just tactical moves for real-world influence.
                </p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                onClick={() => navigate('/get-access')}
                className="glow-button bg-gradient-primary hover:bg-primary/90 text-black px-8 py-6 text-lg font-semibold"
              >
                Get Instant Access - $49
              </Button>
              <Button 
                variant="outline" 
                className="border-primary/30 text-primary hover:bg-primary/10 px-8 py-6 text-lg"
                onClick={onGetStarted}
              >
                Learn More
              </Button>
            </div>

            <p className="text-sm text-muted-foreground">
              ✓ Instant PDF download  ✓ 60-day money-back guarantee  ✓ Secure payment
            </p>
          </div>

          {/* Right content - Book cover */}
          <div className="relative fade-in-up" style={{animationDelay: '0.3s'}}>
            <div className="relative z-10">
              <img 
                src={bookCover} 
                alt="The Dominion Network Playbook"
                className="w-full max-w-md mx-auto rounded-lg shadow-glow"
              />
            </div>
            {/* Glow effect */}
            <div className="absolute inset-0 bg-gradient-primary rounded-lg blur-3xl opacity-20 scale-110" />
          </div>
        </div>
      </div>
    </section>
  );
};