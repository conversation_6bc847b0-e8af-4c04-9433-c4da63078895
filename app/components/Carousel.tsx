import React from "react";
import { Carousel, Card } from "@/components/ui/apple-cards-carousel";
import { <PERSON><PERSON><PERSON>, Star, Zap } from "lucide-react";

const CarouselDemo = () => {
  return (
    <div className="w-full overflow-hidden bg-black py-20">
      <div className="mx-auto max-w-7xl px-4">
        <Carousel>
          {[
            {
              title: "Fast Performance",
              description: "Optimized React + Vite setup.",
              icon: <Zap className="h-4 w-4 text-white" />,
            },
            {
              title: "Smooth Animations",
              description: "Powered by Framer Motion.",
              icon: <Sparkles className="h-4 w-4 text-white" />,
            },
            {
              title: "Beautiful UI",
              description: "Tailwind v4 + Aceternity Style.",
              icon: <Star className="h-4 w-4 text-white" />,
            },
          ].map((item, index) => (
            <Card key={index} {...item} />
          ))}
        </Carousel>
      </div>
    </div>
  );
};

export default CarouselDemo;
