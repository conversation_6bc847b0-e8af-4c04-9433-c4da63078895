{"name": "e-book-dominion", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@cashfreepayments/cashfree-js": "^1.0.5", "@tabler/icons-react": "^3.34.1", "cashfree-pg": "^5.0.8", "clsx": "^2.1.1", "google-auth-library": "^10.2.0", "google-spreadsheet": "^4.1.5", "lucide-react": "^0.525.0", "motion-react": "^0.15.0-alpha.1", "next": "15.4.3", "nodemailer": "^7.0.5", "react": "19.1.0", "react-dom": "19.1.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}